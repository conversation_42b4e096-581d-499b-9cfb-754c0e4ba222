import os
import json
from typing import List, Dict, Any
import logging
from config import AGRICULTURAL_CATEGORIES

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgriculturalKnowledgeLoader:
    def __init__(self):
        self.knowledge_base = []
        
    def load_sample_agricultural_data(self) -> List[Dict[str, Any]]:
        """Load sample agricultural knowledge data"""
        
        sample_data = [
            {
                'content': """Tomato Blight Disease Management:
                
Early blight (Alternaria solani) and late blight (Phytophthora infestans) are common tomato diseases.

Symptoms:
- Early blight: Dark spots with concentric rings on older leaves
- Late blight: Water-soaked spots that turn brown, white fuzzy growth on leaf undersides

Prevention:
1. Crop rotation: Avoid planting tomatoes in the same location for 3-4 years
2. Proper spacing: Ensure good air circulation between plants
3. Drip irrigation: Avoid overhead watering to keep leaves dry
4. Mulching: Use organic mulch to prevent soil splash

Treatment:
- Copper-based fungicides for early stages
- Remove affected plant parts immediately
- Apply preventive fungicide sprays every 7-14 days during humid conditions""",
                'metadata': {
                    'category': 'crop_diseases',
                    'title': 'Tomato Blight Disease Management',
                    'source': 'Agricultural Extension Service'
                }
            },
            {
                'content': """Integrated Pest Management for Aphids:
                
Aphids are small, soft-bodied insects that feed on plant sap and can transmit viruses.

Identification:
- Small (1-4mm), pear-shaped insects
- Colors: green, black, red, or white
- Found on new growth, undersides of leaves
- Produce sticky honeydew

Natural Control Methods:
1. Beneficial insects: Ladybugs, lacewings, parasitic wasps
2. Companion planting: Marigolds, catnip, garlic
3. Reflective mulch: Aluminum foil mulch deters aphids
4. Water spray: Strong water stream to dislodge aphids

Organic Treatments:
- Neem oil spray (2-3% solution)
- Insecticidal soap
- Diatomaceous earth around plants
- Essential oil sprays (peppermint, rosemary)""",
                'metadata': {
                    'category': 'pest_management',
                    'title': 'Integrated Pest Management for Aphids',
                    'source': 'Organic Farming Guide'
                }
            },
            {
                'content': """Soil pH Management and Testing:
                
Soil pH affects nutrient availability and plant health. Most crops prefer slightly acidic to neutral pH (6.0-7.0).

Testing Soil pH:
1. Use digital pH meter for accuracy
2. Test multiple locations in field
3. Test at 6-inch depth
4. Best time: spring before planting

pH Adjustment:
- To raise pH (reduce acidity): Add agricultural lime
  * Calcitic lime for calcium-deficient soils
  * Dolomitic lime for magnesium-deficient soils
  * Apply 3-6 months before planting

- To lower pH (increase acidity): Add sulfur or organic matter
  * Elemental sulfur: 1-2 lbs per 100 sq ft
  * Organic compost: improves buffering capacity

Nutrient Availability by pH:
- pH 6.0-7.0: Optimal for most nutrients
- Below 6.0: Iron, manganese more available; phosphorus less available
- Above 7.0: Iron, zinc deficiencies common""",
                'metadata': {
                    'category': 'soil_management',
                    'title': 'Soil pH Management and Testing',
                    'source': 'Soil Science Institute'
                }
            },
            {
                'content': """Organic Fertilizer Application Guide:
                
Organic fertilizers improve soil health while providing nutrients to plants.

Common Organic Fertilizers:
1. Compost: 1-2% N-P-K, slow release, improves soil structure
2. Aged manure: 2-1-2 N-P-K average, adds organic matter
3. Fish emulsion: 5-1-1 N-P-K, quick-acting nitrogen source
4. Bone meal: 3-15-0 N-P-K, excellent phosphorus source
5. Kelp meal: 1-0-2 N-P-K, provides trace minerals

Application Rates:
- Compost: 2-4 inches annually, work into top 6 inches
- Aged manure: 2-3 inches annually, apply in fall
- Fish emulsion: Dilute 1:5 with water, apply every 2-3 weeks
- Bone meal: 1-2 lbs per 100 sq ft, mix into soil before planting

Timing:
- Spring: Apply slow-release fertilizers before planting
- Growing season: Side-dress with compost or liquid fertilizers
- Fall: Apply compost and aged manure for next season""",
                'metadata': {
                    'category': 'fertilizers',
                    'title': 'Organic Fertilizer Application Guide',
                    'source': 'Organic Farming Association'
                }
            },
            {
                'content': """Water-Efficient Irrigation Techniques:
                
Efficient irrigation conserves water while maintaining crop health and yield.

Drip Irrigation:
- 90-95% water efficiency
- Delivers water directly to root zone
- Reduces disease by keeping foliage dry
- Installation: 12-18 inches apart for vegetables

Soaker Hoses:
- 85-90% efficiency
- Good for row crops and garden beds
- Bury 2-3 inches deep or cover with mulch
- Water pressure: 8-10 PSI optimal

Irrigation Scheduling:
1. Check soil moisture at root depth
2. Water early morning (6-10 AM) to reduce evaporation
3. Deep, infrequent watering promotes deep roots
4. Adjust for weather conditions and plant growth stage

Water Conservation Tips:
- Mulch to reduce evaporation
- Use rain gauges to track natural precipitation
- Install soil moisture sensors
- Group plants by water needs""",
                'metadata': {
                    'category': 'irrigation',
                    'title': 'Water-Efficient Irrigation Techniques',
                    'source': 'Water Conservation Institute'
                }
            },
            {
                'content': """Crop Rotation Planning for Disease Prevention:
                
Crop rotation breaks disease and pest cycles while improving soil health.

Basic Rotation Principles:
1. Avoid planting same family crops consecutively
2. Rotate between heavy feeders, light feeders, and soil builders
3. Include nitrogen-fixing legumes
4. Plan 3-4 year rotation cycles

Sample 4-Year Rotation:
Year 1: Tomatoes/Peppers (heavy feeders)
Year 2: Beans/Peas (nitrogen fixers)
Year 3: Leafy greens (light feeders)
Year 4: Root vegetables (soil breakers)

Plant Families to Rotate:
- Solanaceae: Tomatoes, peppers, eggplant, potatoes
- Brassicaceae: Cabbage, broccoli, radishes, turnips
- Leguminosae: Beans, peas, lentils
- Cucurbitaceae: Cucumbers, squash, melons

Benefits:
- Reduces soil-borne diseases by 60-80%
- Breaks pest life cycles
- Improves soil fertility naturally
- Reduces need for chemical inputs""",
                'metadata': {
                    'category': 'crop_diseases',
                    'title': 'Crop Rotation Planning for Disease Prevention',
                    'source': 'Sustainable Agriculture Research'
                }
            }
        ]
        
        logger.info(f"Loaded {len(sample_data)} sample agricultural documents")
        return sample_data
        
    def load_from_directory(self, directory_path: str) -> List[Dict[str, Any]]:
        """Load agricultural documents from a directory"""
        documents = []
        
        if not os.path.exists(directory_path):
            logger.warning(f"Directory {directory_path} does not exist")
            return documents
            
        for filename in os.listdir(directory_path):
            if filename.endswith('.txt'):
                file_path = os.path.join(directory_path, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as file:
                        content = file.read()
                        
                    # Determine category from filename or content
                    category = self._determine_category(filename, content)
                    
                    documents.append({
                        'content': content,
                        'metadata': {
                            'category': category,
                            'title': filename.replace('.txt', '').replace('_', ' ').title(),
                            'source': f'File: {filename}'
                        }
                    })
                    
                except Exception as e:
                    logger.error(f"Error reading file {filename}: {e}")
                    
        logger.info(f"Loaded {len(documents)} documents from {directory_path}")
        return documents
        
    def _determine_category(self, filename: str, content: str) -> str:
        """Determine the category of a document based on filename and content"""
        filename_lower = filename.lower()
        content_lower = content.lower()
        
        # Check filename for category keywords
        for category in AGRICULTURAL_CATEGORIES:
            if category.replace('_', '') in filename_lower.replace('_', ''):
                return category
                
        # Check content for category keywords
        category_keywords = {
            'crop_diseases': ['disease', 'pathogen', 'fungus', 'bacteria', 'virus', 'blight', 'rot'],
            'pest_management': ['pest', 'insect', 'aphid', 'beetle', 'caterpillar', 'mite'],
            'soil_management': ['soil', 'ph', 'nutrients', 'compost', 'organic matter'],
            'fertilizers': ['fertilizer', 'nitrogen', 'phosphorus', 'potassium', 'npk'],
            'irrigation': ['water', 'irrigation', 'drip', 'sprinkler', 'moisture'],
            'weather_patterns': ['weather', 'climate', 'temperature', 'rainfall', 'drought'],
            'harvesting': ['harvest', 'picking', 'storage', 'post-harvest'],
            'seed_selection': ['seed', 'variety', 'cultivar', 'planting'],
            'organic_farming': ['organic', 'sustainable', 'natural', 'biological'],
            'livestock': ['livestock', 'cattle', 'poultry', 'animal', 'feed']
        }
        
        for category, keywords in category_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                return category
                
        return 'general'
        
    def chunk_documents(self, documents: List[Dict[str, Any]], chunk_size: int = 1000, overlap: int = 200) -> List[Dict[str, Any]]:
        """Split large documents into smaller chunks"""
        chunked_docs = []
        
        for doc in documents:
            content = doc['content']
            metadata = doc['metadata']
            
            if len(content) <= chunk_size:
                chunked_docs.append(doc)
                continue
                
            # Split into chunks with overlap
            for i in range(0, len(content), chunk_size - overlap):
                chunk = content[i:i + chunk_size]
                
                chunk_metadata = metadata.copy()
                chunk_metadata['chunk_index'] = len(chunked_docs)
                chunk_metadata['is_chunk'] = True
                
                chunked_docs.append({
                    'content': chunk,
                    'metadata': chunk_metadata
                })
                
        logger.info(f"Created {len(chunked_docs)} chunks from {len(documents)} documents")
        return chunked_docs
