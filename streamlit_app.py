import streamlit as st
import requests
import json
from typing import Dict, Any

# Page configuration
st.set_page_config(
    page_title="Agricultural Chatbot",
    page_icon="🌱",
    layout="wide",
    initial_sidebar_state="expanded"
)

# API Configuration
API_BASE_URL = "http://localhost:8000"

def check_api_health() -> bool:
    """Check if the API is running"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def get_sample_questions() -> list:
    """Get sample questions from API"""
    try:
        response = requests.get(f"{API_BASE_URL}/sample-questions")
        if response.status_code == 200:
            return response.json()["sample_questions"]
    except:
        pass
    return [
        "How do I treat tomato blight disease?",
        "What are the best methods for controlling aphids?",
        "How do I test and adjust soil pH?",
        "What organic fertilizers should I use for vegetables?"
    ]

def send_chat_message(query: str) -> Dict[str, Any]:
    """Send chat message to API"""
    try:
        response = requests.post(
            f"{API_BASE_URL}/chat",
            json={"query": query, "max_sources": 5},
            timeout=30
        )
        if response.status_code == 200:
            return response.json()
        else:
            return {
                "status": "error",
                "error": f"API returned status code {response.status_code}"
            }
    except Exception as e:
        return {
            "status": "error",
            "error": f"Failed to connect to API: {str(e)}"
        }

# Main app
def main():
    st.title("🌱 Agricultural Chatbot with RAG")
    st.markdown("*Your AI-powered agricultural advisor using advanced retrieval and generation*")
    
    # Sidebar
    with st.sidebar:
        st.header("🔧 System Status")
        
        # Check API health
        if check_api_health():
            st.success("✅ API is running")
        else:
            st.error("❌ API is not responding")
            st.markdown("**To start the API:**")
            st.code("python app.py", language="bash")
            st.stop()
        
        st.header("📚 Sample Questions")
        sample_questions = get_sample_questions()
        
        for i, question in enumerate(sample_questions[:6]):  # Show first 6
            if st.button(f"💡 {question[:50]}...", key=f"sample_{i}"):
                st.session_state.selected_question = question
    
    # Initialize session state
    if "messages" not in st.session_state:
        st.session_state.messages = []
    
    if "selected_question" in st.session_state:
        st.session_state.messages.append({
            "role": "user",
            "content": st.session_state.selected_question
        })
        del st.session_state.selected_question
        st.rerun()
    
    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
            
            # Display sources for assistant messages
            if message["role"] == "assistant" and "sources" in message:
                if message["sources"]:
                    with st.expander("📖 Sources Used"):
                        for i, source in enumerate(message["sources"]):
                            st.markdown(f"""
                            **{i+1}. {source['title']}**
                            - Category: {source['category']}
                            - Relevance Score: {source['relevance_score']:.3f}
                            """)
    
    # Chat input
    if prompt := st.chat_input("Ask me anything about agriculture..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Get bot response
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                response = send_chat_message(prompt)
            
            if response["status"] == "success":
                st.markdown(response["response"])
                
                # Display sources
                if response["sources"]:
                    with st.expander("📖 Sources Used"):
                        for i, source in enumerate(response["sources"]):
                            st.markdown(f"""
                            **{i+1}. {source['title']}**
                            - Category: {source['category']}
                            - Relevance Score: {source['relevance_score']:.3f}
                            """)
                
                # Add assistant message to chat history
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": response["response"],
                    "sources": response["sources"]
                })
            else:
                error_msg = f"❌ Error: {response.get('error', 'Unknown error')}"
                st.error(error_msg)
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": error_msg
                })
    
    # Footer
    st.markdown("---")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🗑️ Clear Chat"):
            st.session_state.messages = []
            st.rerun()
    
    with col2:
        st.markdown("**Powered by:**")
        st.markdown("🤖 Google Gemini • 🔍 Pinecone • ⚡ FastAPI")
    
    with col3:
        st.markdown("**Categories:**")
        st.markdown("🌾 Crops • 🐛 Pests • 🌱 Soil • 💧 Irrigation")

if __name__ == "__main__":
    main()
